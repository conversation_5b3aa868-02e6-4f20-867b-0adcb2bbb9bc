/**
 * Vitest Configuration for Authentication Tests
 * Comprehensive test configuration for TEST-002 through TEST-010
 */

import { defineConfig } from 'vitest/config'
import { resolve } from 'path'

export default defineConfig({
  test: {
    // Test environment
    environment: 'jsdom',
    
    // Global setup and teardown
    globalSetup: './test/setup/global-setup.js',
    setupFiles: ['./test/setup/test-setup.js'],
    
    // Test patterns
    include: [
      'test/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'ui/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
    ],
    exclude: [
      'node_modules',
      'dist',
      '.nuxt',
      'coverage',
      'test/fixtures',
    ],
    
    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './test/coverage',
      include: [
        'ui/stores/**/*.js',
        'ui/composables/**/*.js',
        'ui/middleware/**/*.js',
        'common/services/**/*.js',
        'api/services/**/*.js',
      ],
      exclude: [
        'test/**',
        '**/*.test.js',
        '**/*.spec.js',
        '**/node_modules/**',
        '**/.nuxt/**',
        '**/dist/**',
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
        // Specific thresholds for authentication modules
        'ui/stores/auth.js': {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90,
        },
        'common/services/pocketbase.js': {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85,
        },
      },
    },
    
    // Test timeout
    testTimeout: 10000,
    hookTimeout: 10000,
    
    // Reporters
    reporter: ['verbose', 'json', 'html'],
    outputFile: {
      json: './test/results/test-results.json',
      html: './test/results/test-results.html',
    },
    
    // Mock configuration
    deps: {
      inline: ['vue', '@vue/test-utils', 'pinia'],
    },
    
    // Alias configuration
    alias: {
      '@': resolve(__dirname, '../'),
      '~': resolve(__dirname, '../'),
      'ui': resolve(__dirname, '../ui'),
      'common': resolve(__dirname, '../common'),
      'api': resolve(__dirname, '../api'),
      'test': resolve(__dirname, '../test'),
    },
    
    // Test categorization
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        isolate: true,
      },
    },
    
    // Retry configuration for flaky tests
    retry: 2,
    
    // Watch configuration
    watch: false,
    
    // Browser configuration for E2E tests
    browser: {
      enabled: false, // Enable for E2E tests
      name: 'chromium',
      provider: 'playwright',
      headless: true,
      screenshotOnFailure: true,
    },
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': resolve(__dirname, '../'),
      '~': resolve(__dirname, '../'),
      'ui': resolve(__dirname, '../ui'),
      'common': resolve(__dirname, '../common'),
      'api': resolve(__dirname, '../api'),
      'test': resolve(__dirname, '../test'),
    },
  },
  
  // Define configuration for different test types
  define: {
    __TEST__: true,
    __DEV__: true,
  },
})

// Export test configurations for different test suites
export const unitTestConfig = defineConfig({
  ...this.default,
  test: {
    ...this.default.test,
    include: [
      'test/unit/**/*.{test,spec}.{js,ts}',
      'ui/stores/__tests__/**/*.{test,spec}.{js,ts}',
    ],
    name: 'unit',
  },
})

export const integrationTestConfig = defineConfig({
  ...this.default,
  test: {
    ...this.default.test,
    include: [
      'test/integration/**/*.{test,spec}.{js,ts}',
    ],
    name: 'integration',
    testTimeout: 15000,
  },
})

export const e2eTestConfig = defineConfig({
  ...this.default,
  test: {
    ...this.default.test,
    include: [
      'test/e2e/**/*.{test,spec}.{js,ts}',
    ],
    name: 'e2e',
    testTimeout: 30000,
    browser: {
      enabled: true,
      name: 'chromium',
      provider: 'playwright',
      headless: true,
      screenshotOnFailure: true,
    },
  },
})
