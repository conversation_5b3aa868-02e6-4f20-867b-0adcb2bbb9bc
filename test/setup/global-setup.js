/**
 * Global Test Setup
 * Runs once before all tests
 */

import { vi } from 'vitest'

export async function setup() {
  console.log('🧪 Setting up global test environment...')
  
  // Set test environment variables
  process.env.NODE_ENV = 'test'
  process.env.NUXT_ENV = 'test'
  
  // Mock environment variables
  process.env.POCKETBASE_URL = 'http://localhost:8090'
  process.env.POCKETBASE_ADMIN_EMAIL = '<EMAIL>'
  process.env.POCKETBASE_ADMIN_PASSWORD = 'testpassword'
  
  // Set up global test configuration
  global.__TEST__ = true
  global.__DEV__ = true
  
  console.log('✅ Global test environment setup complete')
}

export async function teardown() {
  console.log('🧹 Cleaning up global test environment...')
  
  // Clean up any global resources
  vi.clearAllMocks()
  
  console.log('✅ Global test environment cleanup complete')
}

export default {
  setup,
  teardown,
}
